const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const { auctionEndTimes, getAuctionEndTime, getAllAuctionIds } = require('../shared/auctionData.cjs');

// Function to get auction data from frontend data.ts
function getAuctionDataFromFrontend() {
  // This maps auction IDs to their current bid and bid count from data.ts
  return {
    'fe0': { currentBid: 20, bidCount: 0 },
    'fe1': { currentBid: 15, bidCount: 0 },
    'fe2': { currentBid: 110, bidCount: 1 },
    'fe3': { currentBid: 320, bidCount: 0 },
    'fe4': { currentBid: 4200, bidCount: 1 },
    'fe5': { currentBid: 5, bidCount: 0 },
    'fe6': { currentBid: 28, bidCount: 19 },
    'fe7': { currentBid: 16, bidCount: 12 },
    'fe8': { currentBid: 35, bidCount: 21 },
    'fe9': { currentBid: 50, bidCount: 67 },
    'fe10': { currentBid: 30, bidCount: 43 },
    'fe11': { currentBid: 447, bidCount: 18 },
    'fe12': { currentBid: 127500, bidCount: 87 },
    'fe13': { currentBid: 4200, bidCount: 31 },
    'fe14': { currentBid: 450, bidCount: 12 },
    'fe15': { currentBid: 125, bidCount: 28 },
    'pokemon-cards': { currentBid: 2800, bidCount: 35 },
    'first-class-upgrade': { currentBid: 1200, bidCount: 22 },
    'beats-earphones': { currentBid: 180, bidCount: 15 },
    'bulgari-wonders': { currentBid: 1500, bidCount: 0 }
  };
}

const app = express();
const server = http.createServer(app);

// Configure CORS for Socket.IO
const io = socketIo(server, {
  cors: {
    origin: ["http://localhost:8080", "http://localhost:8081"], // Your frontend URLs
    methods: ["GET", "POST"],
    credentials: true
  }
});

app.use(cors());
app.use(express.json());

// Store active auctions and their current state
const auctions = new Map();
const completedAuctions = new Map(); // Store completed auctions with winners

// Initialize auctions using shared data
const startTime = Date.now();
const frontendAuctionData = getAuctionDataFromFrontend();

// Initialize all auctions from shared configuration
getAllAuctionIds().forEach(id => {
  // Get the predefined data from frontend, or use defaults if not found
  const auctionData = frontendAuctionData[id] || { currentBid: 100, bidCount: 0 };

  auctions.set(id, {
    id,
    currentBid: auctionData.currentBid,
    bidCount: auctionData.bidCount,
    bids: [],
    startTime: startTime,
    endTime: getAuctionEndTime(id, startTime),
    status: 'active'
  });
});

console.log(`🎯 Initialized ${auctions.size} auctions from shared configuration`);



// Auction completion monitoring
function checkAuctionCompletion() {
  const now = Date.now();

  for (const [auctionId, auction] of auctions.entries()) {
    if (auction.status === 'active' && now >= auction.endTime) {
      // Auction has ended
      auction.status = 'completed';

      // Determine winner (highest bidder)
      let winner = null;
      if (auction.bids.length > 0) {
        // Get the most recent bid (which should be the highest due to validation)
        winner = auction.bids[0];
      }

      // Add winner information to the auction object
      auction.winner = winner;
      auction.completedAt = now;

      // Store completed auction (keep a copy in completedAuctions for reference)
      completedAuctions.set(auctionId, {
        ...auction,
        winner: winner,
        completedAt: now
      });

      console.log(`Auction ${auctionId} completed. Winner: ${winner ? winner.bidder.name : 'No bids'}`);

      // Broadcast auction completion to all clients in the auction room
      io.to(auctionId).emit('auction_ended', {
        auctionId,
        winner: winner,
        finalBid: auction.currentBid,
        completedAt: now
      });

      // Keep the auction in the auctions map but mark it as completed
      // Do NOT remove from active auctions - just update status
    }
  }
}

// Check for completed auctions every 5 seconds
setInterval(checkAuctionCompletion, 5000);

// Socket.IO connection handling
io.on('connection', (socket) => {
  console.log(`User connected: ${socket.id}`);

  // Handle time synchronization request
  socket.on('time_sync_request', (data) => {
    const serverTime = Date.now();

    // Handle both old format (just clientTime) and new format (object with clientTime)
    const clientTime = typeof data === 'object' ? data.clientTime : data;

    console.log(`Time sync request from ${socket.id}: client=${clientTime}, server=${serverTime}, diff=${serverTime - clientTime}ms`);

    socket.emit('time_sync_response', {
      clientTime,
      serverTime,
      roundTripTime: 0 // Will be calculated on client side
    });
  });

  // Handle auction subscription
  socket.on('subscribe_auction', (auctionId) => {
    console.log(`User ${socket.id} subscribed to auction ${auctionId}`);
    socket.join(auctionId);

    // Send current auction state to the newly connected user
    const auction = auctions.get(auctionId);
    if (auction) {
      socket.emit('auction_update', {
        auctionId,
        currentBid: auction.currentBid,
        bidCount: auction.bidCount,
        endTime: auction.endTime, // Include endTime from backend
        serverTime: Date.now() // Include server time for sync
      });
    }
  });

  // Handle auction unsubscription
  socket.on('unsubscribe_auction', (auctionId) => {
    console.log(`User ${socket.id} unsubscribed from auction ${auctionId}`);
    socket.leave(auctionId);
  });

  // Handle new bid placement
  socket.on('place_bid', (data) => {
    const { auctionId, username, price, flightNumber } = data;
    console.log(`New bid from ${username}${flightNumber ? ` (Flight: ${flightNumber})` : ''}: $${price} for auction ${auctionId}`);

    // Get auction data
    const auction = auctions.get(auctionId);
    if (!auction) {
      socket.emit('bid_error', { message: 'Auction not found' });
      return;
    }

    // Check if auction is still active
    if (auction.status !== 'active') {
      socket.emit('bid_error', { message: 'Auction has ended' });
      return;
    }

    // Check if auction has expired
    if (Date.now() >= auction.endTime) {
      socket.emit('bid_error', { message: 'Auction has ended' });
      return;
    }

    // Validate bid amount
    if (price <= auction.currentBid) {
      socket.emit('bid_error', {
        message: `Bid must be higher than current bid of $${auction.currentBid}`
      });
      return;
    }

    // Create new bid object with server time
    const serverTime = Date.now();
    const newBid = {
      id: `bid-${serverTime}-${Math.random().toString(36).substring(2, 11)}`,
      auctionId,
      amount: price,
      bidder: {
        name: username,
        avatar: "https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=100&h=100&fit=crop&crop=face",
        ...(flightNumber && { flightNumber })
      },
      timestamp: new Date(serverTime),
      serverTime: serverTime
    };

    // Update auction state
    auction.currentBid = price;
    auction.bidCount += 1;
    auction.bids.unshift(newBid);

    // Keep only last 50 bids to prevent memory issues
    if (auction.bids.length > 50) {
      auction.bids = auction.bids.slice(0, 50);
    }

    // Broadcast the new bid to all users in the auction room
    io.to(auctionId).emit('new_bid', {
      bid: newBid,
      serverTime: serverTime
    });

    // Also broadcast auction update
    io.to(auctionId).emit('auction_update', {
      auctionId,
      currentBid: auction.currentBid,
      bidCount: auction.bidCount,
      endTime: auction.endTime, // Include endTime from backend
      serverTime: serverTime
    });

    console.log(`Bid processed: ${username} bid $${price} for auction ${auctionId}`);
  });

  // Handle disconnection
  socket.on('disconnect', () => {
    console.log(`User disconnected: ${socket.id}`);
  });
});

// Time synchronization endpoint
app.get('/api/time', (req, res) => {
  res.json({
    serverTime: Date.now(),
    timestamp: new Date().toISOString()
  });
});

// Flight number route information endpoint
app.get('/api/flight/:flightNumber', (req, res) => {
  const { flightNumber } = req.params;

  // Basic flight number validation
  if (!/^[A-Z]{2,3}[0-9]{1,4}[A-Z]?$/i.test(flightNumber)) {
    return res.status(400).json({
      error: 'Invalid flight number format'
    });
  }

  // Mock flight route data (in a real app, this would come from an airline API)
  const mockRoutes = {
    'AA123': { from: 'JFK', to: 'LAX', airline: 'American Airlines' },
    'BA456': { from: 'LHR', to: 'JFK', airline: 'British Airways' },
    'UA789': { from: 'SFO', to: 'ORD', airline: 'United Airlines' },
    'DL101': { from: 'ATL', to: 'MIA', airline: 'Delta Air Lines' },
    'SW202': { from: 'LAS', to: 'PHX', airline: 'Southwest Airlines' }
  };

  const route = mockRoutes[flightNumber.toUpperCase()];

  if (route) {
    res.json({
      flightNumber: flightNumber.toUpperCase(),
      ...route,
      status: 'found'
    });
  } else {
    res.json({
      flightNumber: flightNumber.toUpperCase(),
      from: 'Unknown',
      to: 'Unknown',
      airline: 'Unknown',
      status: 'not_found'
    });
  }
});

// Basic health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    activeConnections: io.engine.clientsCount,
    activeAuctions: auctions.size
  });
});

// Get auction data endpoint
app.get('/api/auction/:id', (req, res) => {
  const auction = auctions.get(req.params.id);
  if (!auction) {
    return res.status(404).json({ error: 'Auction not found' });
  }
  res.json({
    ...auction,
    serverTime: Date.now() // Include current server time for sync
  });
});

// Get all auctions endpoint
app.get('/api/auctions', (req, res) => {
  const auctionList = Array.from(auctions.values()).map(auction => ({
    ...auction,
    serverTime: Date.now()
  }));
  res.json(auctionList);
});

// Get shared auction configuration endpoint
app.get('/api/auction-config', (req, res) => {
  res.json({
    auctionEndTimes,
    totalAuctions: getAllAuctionIds().length,
    availableAuctionIds: getAllAuctionIds(),
    serverTime: Date.now()
  });
});

const PORT = process.env.PORT || 3001;

server.listen(PORT, () => {
  console.log(`🚀 Live Auction Server running on port ${PORT}`);
  console.log(`📡 Socket.IO server ready for connections`);
  console.log(`🔗 Frontend should connect to: http://localhost:${PORT}`);
});
