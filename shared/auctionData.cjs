// Shared auction data for both frontend and backend
// This file provides a single source of truth for auction end times

// Auction end times configuration (in seconds from now)
const auctionEndTimes = {
  'fe0': 35,   // 30 seconds
  'fe1': 60,   // 1 minute
  'fe2': 100,   // 1.5 minutes
  'fe3': 140,  // 2 minutes
  'fe4': 150,  // 2.5 minutes
  'fe5': 180,  // 3 minutes
  'fe6': 210,  // 3.5 minutes
  'fe7': 240,  // 4 minutes
  'fe8': 240,   // 1.5 minutes
  'fe9': 240,   // 1 minute
  'fe10': 240,  // 1 minute
  'fe11': 240,  // 1 minute
  'fe12': 240,  // 1 minute
  'fe13': 240,  // 1 minute
  'fe14': 240,  // 1.2 minutes
  'fe15': 240,  // 1.2 minutes
  'pokemon-cards': 240,     // 1.2 minutes
  'first-class-upgrade': 240,  // 1.5 minutes
  'beats-earphones': 240,     // 1.8 minutes
  'bulgari-wonders': 240,      // 1 minute
};

// Default auction configuration
const defaultAuctionConfig = {
  startingBid: 100,
  bidIncrement: 5,
  defaultDuration: 60, // 1 minute default
};

// Helper function to get auction end time
function getAuctionEndTime(auctionId, baseTime = Date.now()) {
  const duration = auctionEndTimes[auctionId] || defaultAuctionConfig.defaultDuration;
  return baseTime + (duration * 1000);
}

// Helper function to get all auction IDs
function getAllAuctionIds() {
  return Object.keys(auctionEndTimes);
}

// Helper function to check if auction ID exists
function isValidAuctionId(auctionId) {
  return auctionEndTimes.hasOwnProperty(auctionId);
}

module.exports = {
  auctionEndTimes,
  defaultAuctionConfig,
  getAuctionEndTime,
  getAllAuctionIds,
  isValidAuctionId
};
